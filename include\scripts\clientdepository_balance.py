import configparser
import ast
from datetime import datetime, date, timedelta
import pandas as pd
import os
from credentials import  db_conn
from datetime import datetime


dir_path = os.path.dirname(os.path.realpath('__file__'))

config = configparser.ConfigParser()
config.read(f"{dir_path}/config.ini")

config_source = 'MODELED_SOURCE_DB'

# connection
source_engine,conn_source =  db_conn(conn_param=config_source)
#target_engine,conn_target =  db_conn(conn_param=config_target)


# Define the query to check for existing records for today's date
check_query = """
SELECT COUNT(*) as count
FROM exchange_mart.fact_client_dailydepositorybalance
WHERE DATE(depository_date) = (CURRENT_DATE - 1)
"""


# Execute the check query
existing_count = pd.read_sql_query(check_query, source_engine).iloc[0]['count']


# If there are existing records for today, delete them
if existing_count > 0:
    delete_query = """
    DELETE FROM exchange_mart.fact_client_dailydepositorybalance
    WHERE DATE(depository_date) = (CURRENT_DATE - 1)
    """
    with source_engine.connect() as connection:
        connection.execute(delete_query)
        

# Get the latest portfolio balance for all clients
latest_balance = """
with base_table as (
	select *, coalesce(location_id, 0) as adj_location_id			
	from csd.depository_csdoperationlogging
	),
	
adjusted_location_id as (
	
	select client.cid, logg.client_id id_index, DATE(logg.created) depository_date,
			logg.*, pdt.name product, pdt.code product_code, pdt.product_type, loc.name location_name, loc.state location_state
	
	from base_table logg
	left join csd.crm_client client 
		on logg.client_id = client.id
	left join csd.crm_product pdt
		on logg.product_id = pdt.id
	left join csd.depository_location loc
		on logg.adj_location_id = loc.id
	),
	
indexed_table as (
	select row_number() over (partition by id_index, product_id, adj_location_id order by created desc) daily_index, *
	from adjusted_location_id
	)

select 
	depository_date, cid, product_id, product, product_code, product_type, adj_location_id location_id, location_name, location_state,
 	lien_volume_after latest_lien_volume,
	total_volume_after latest_total_volume,
	available_volume_after latest_available_volume,
    created latest_trans_at

from indexed_table
where daily_index = 1
order by depository_date, cid, product_id, adj_location_id
"""

hot_data = pd.read_sql_query(latest_balance,source_engine)

fin_data = hot_data[['depository_date', 'cid', 'product_id', 'product', 
						'product_code', 'product_type', 'location_id', 'location_name', 
						'location_state','latest_lien_volume','latest_total_volume','latest_available_volume',
                        'latest_trans_at']]

# change the latest depository_date to the current date
yesterday = (date.today()) - timedelta(days=1)
fin_data.depository_date = yesterday.strftime("%Y-%m-%d")
print('\n\n\ntoday_date :',yesterday)

fin_data.to_sql('fact_client_dailydepositorybalance',source_engine, schema= 'exchange_mart', index=False, if_exists="append")
{{ config (materialized = 'ephemeral') }}

with
closing_opening_price as(
	select date, extract(dow from price.date::timestamp) dow
			, concat(extract(year from price.date::timestamp), '_', extract(week from price.date::timestamp)) woy
			, price.commodity_code
			, price.commodity_name commodity
			, price.commodity_closing_price closing_price
			, lag(price.commodity_closing_price, 1) over (partition by price.commodity_code order by price.date) opening_price
	
	from {{ ref ('ovs_int_prices_commodities_nonnull_prices')}} price
	
	where date >= '2020-01-01' 
	),	

trade_transactions as (
	select *
	
	from {{ source ('exchange_mart', 'fact_trade_individual_transactions') }}
	),
	
high_low_prices as(
	select date(deal_created_at) date
			, commodity
			, max(matched_price/volume_per_unit) max_price_kg
			, min(matched_price/volume_per_unit) min_price_kg
			 
	from trade_transactions
	where order_type = 'Buy' 
			and (oms_name = 'COMX' or oms_name is null or oms_name = '')
			and (currency = 'Naira' or currency is null or currency = '')
	
	group by date(deal_created_at), commodity
	order by date(deal_created_at), commodity
	),	

securities_prices as(
	select closing_opening_price.*
			, high_low_prices.max_price_kg
			, high_low_prices.min_price_kg

	from closing_opening_price
	left join high_low_prices
		on closing_opening_price.date = high_low_prices.date
		and closing_opening_price.commodity = high_low_prices.commodity
	),

non_null_minmax as( -- When max or min prices are null, becuase no trade on that day, then closing price
	select date, dow, woy, commodity_code, commodity 
			, closing_price
			, opening_price
			, case when (max_price_kg is null)  and (closing_price >= opening_price) then closing_price
					when (max_price_kg is null)  and (closing_price < opening_price) then opening_price
				else max_price_kg
				end max_price_kg
			, case when (min_price_kg is null) and (closing_price > opening_price) then opening_price
					when (min_price_kg is null) and (closing_price <= opening_price) then closing_price
				else min_price_kg
				end min_price_kg			
	
	from securities_prices
	)

select *
from non_null_minmax
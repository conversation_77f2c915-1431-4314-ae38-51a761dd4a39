{{ config(materialized='table') }}


with country as (
    select  trim(both  'b\''' FROM name) country,
            trim(both  'b\''' FROM alpha2code) code,
            trim(both  'b\''' FROM region) region,
            trim(both  'b\''' FROM subregion) subregion
    from {{ source ('comx','account_country') }}         
),

final as (
    select client.*
            , country.country
            , country.region
            , country.subregion


    from {{ source ('comx','crm_client') }} client
    left join country
        on client.country_code = country.code

        where client.cid != ''
)

select 
        id,
        cid,
        rnb,
        email,
        phone,
        address,
        created,
        updated,
        last_name,
        first_name,
        share_code,
        is_approved,
        account_type,
        country_code,
        freshdesk_id,
        is_certified,
        is_synced_wb,
        referral_code,
        verify_me_dob,
        is_afex_broker,
        is_id_verified,
        mismatch_value,
        is_bvn_verified,
        is_kyc_complete,
        is_kyc_rejected,
        is_kyc_verified,
        is_nin_verified,
        client_broker_id,
        is_kyc_submitted,
        failed_cid_reason,
        is_update_pending,
        user_account_type,
        alternative_emails,
        bvn_error_response,
        used_referral_code,
        verify_me_lastname,
        verify_me_firstname,
        verify_me_middlename,
        id_verification_message,
        is_kyc_pending_approval,
        bvn_verification_message,
        nin_verification_message,
        bank_verification_message,
        kyc_verification_failed_date,
        is_brokerage_bank_account_verified,
        country,
        region,
        subregion

from final
{{ config (materialized = 'ephemeral') }}

with
price as (
	-- To retrieve the commodities' closing prices
	-- The upper part of the union includes closing prices from an excel sheet that doesn't live in the Database.
	-- Pre June 15, 2023 data only were utilized because the on-system closing price data starts on 15/06/2023
	-- Lower part is the computed closing price from the exchange.

	
	select date(date)
			, case when commodity = 'Sorghum' then 'SGM'
					when commodity = 'Soybean' then 'SBS'
					when commodity = 'Paddy Rice' then 'PRL'
					when commodity = 'Maize' then 'MAZ'
					when commodity = 'Cocoa' then 'CCO'
					when commodity = 'Ginger' then 'GNG'
				else commodity
				end commodity_code
			, case when commodity = 'Sorghum' then 'Sorghum'
					when commodity = 'Soybean' then 'Soybean'
					when commodity = 'Paddy Rice' then 'Paddy Rice Long Grain'
					when commodity = 'Maize' then 'Maize Feed Grade - White'
					when commodity = 'Cocoa' then 'Cocoa'
					when commodity = 'Ginger' then 'Ginger Dried Split'
				else commodity
				end commodity_name
			, (price * 10)/1000 commodity_closing_price -- original price is by bag
			, 'old' source
	
	from {{ source ('exchange_mart', 'historical_commoditiesprices_upto_2024_02_23') }}
	-- where commodity in ('Maize'
	-- 						, 'Soybean'
	-- 						, 'Paddy Rice'
	-- 						, 'Sorghum'
	-- 						, 'Cocoa'
	-- 						, 'Ginger Dried Split')
	
	
	UNION
	
	
	select price.date
			, price.commodity_code
			, comm.name commodity_name
			, price.price commodity_closing_price
			, 'new' source

	from {{ source ('ovs', 'databank_commodityhistoricalprice') }} price
	left join {{ source ('ovs', 'crm_commodity') }} comm
		on price.commodity_code = comm.code

	where price.price != 0
	-- where price.commodity_code in ('MAZ'
	-- 								, 'SBS'
	-- 								, 'PRL'
	-- 								, 'SGM'
	-- 								, 'CCO'
	-- 								, 'GNG')
	)

select *

from price
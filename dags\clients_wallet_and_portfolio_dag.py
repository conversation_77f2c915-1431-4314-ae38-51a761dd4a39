from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import <PERSON><PERSON><PERSON>perator
from custom_functions.airflow_email_util import failure_email

PATH_TO_SCRIPT_FOLDER = "/usr/local/airflow/include/scripts"

default_args  = {
     'owner' : 'Oluwatomisin Soetan',
     'retries' : 0,
     'retry_delay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}
with DAG(
    dag_id = 'clients_wallet_and_portfolio',
    default_args = default_args,
    description =  'Exchange and Cuide clients daily wallet balance and exchange daily portfolio balance',
    start_date =  datetime(2023, 5, 7),
    catchup=False,
    schedule_interval =  '0 2 * * *' ### this uses utc time
) as dag:
    
    task1 =  BashOperator(
        task_id = 'exchange_clients_wallet',
        bash_command = 'python3 exchangeclients_wallet_balance.py',
        cwd=PATH_TO_SCRIPT_FOLDER,
    )

    task2 =  BashOperator(
        task_id = 'exchange_clients_portfolio',
        bash_command = 'python3 clientportfolio_balance.py',
        cwd=PATH_TO_SCRIPT_FOLDER,
    )

    task3 =  BashOperator(
        task_id = 'cudie_clients_wallet',
        bash_command = 'python3 cudieclients_wallet_balance.py',
        cwd=PATH_TO_SCRIPT_FOLDER,
    )


task1 >> task2
task3
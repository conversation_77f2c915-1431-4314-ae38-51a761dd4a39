version: 2

models:
  - name: fact_exchange_client_walletbalance
    description: "Daily wallet balance fact table for exchange clients, tracking the latest wallet balances per client per currency per day"
    
    columns:
      - name: wallet_date
        description: "Date of the wallet balance snapshot (DATE type)"
        data_type: date
        tests:
          - not_null
        
      - name: cid
        description: "Client identifier - unique client code (VARCHAR(25))"
        data_type: varchar(25)
        tests:
          - not_null
        
      - name: currency
        description: "Currency code for the wallet balance (TEXT type)"
        data_type: text
        tests:
          - not_null
        
      - name: total_wallet_balance
        description: "Total wallet balance amount after the latest transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: lien_wallet_balance
        description: "Lien (locked/encumbered) wallet balance amount after the latest transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: available_wallet_balance
        description: "Available wallet balance amount after the latest transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: latest_trans_at
        description: "Timestamp of the latest transaction that determined this balance (TIMESTAMP)"
        data_type: timestamp

    meta:
      source_table: "csd.wallet_walletoperationlogging"
      target_schema: "exchange_mart"
      primary_key: ["wallet_date", "cid", "currency"]
      data_source: "Exchange wallet transaction logging system"
      update_frequency: "Daily"
      business_logic: "Captures the latest wallet balance per client per currency per day by selecting the most recent transaction for each client-currency combination"

  - name: fact_clientdailyportfoliobalance
    description: "Daily portfolio balance fact table for exchange clients, tracking the latest portfolio holdings per client per security per location per day"
    
    columns:
      - name: portfolio_date
        description: "Date of the portfolio balance snapshot (DATE type)"
        data_type: date
        tests:
          - not_null
        
      - name: cid
        description: "Client identifier - unique client code (VARCHAR(25))"
        data_type: varchar(25)
        tests:
          - not_null
        
      - name: security_name
        description: "Name of the security/instrument (TEXT type)"
        data_type: text
        
      - name: security_code
        description: "Code/symbol of the security/instrument (TEXT type)"
        data_type: text
        
      - name: security_type
        description: "Type/category of the security (e.g., FI, Spot, Dawa) (TEXT type)"
        data_type: text
        
      - name: location_code
        description: "Code of the location where the security is held (TEXT type)"
        data_type: text
        
      - name: location_name
        description: "Name of the location where the security is held (TEXT type)"
        data_type: text
        
      - name: location_state
        description: "State of the location where the security is held (TEXT type)"
        data_type: text
        
      - name: latest_lien_units
        description: "Latest lien (locked/encumbered) units of the security after the most recent transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: latest_available_units
        description: "Latest available units of the security after the most recent transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: latest_total_units
        description: "Latest total units of the security after the most recent transaction (DOUBLE PRECISION)"
        data_type: double precision
        
      - name: latest_trans_at
        description: "Timestamp of the latest transaction that determined this portfolio balance (TIMESTAMP)"
        data_type: timestamp

    meta:
      source_table: "ovs.operation_portfoliologging"
      target_schema: "exchange_mart"
      data_source: "Exchange portfolio operation logging system"
      update_frequency: "Daily"
      business_logic: "Captures the latest portfolio balance per client per security per location per day by selecting the most recent transaction for each client-security-location combination. Excludes Dawa securities with null location_id. For FI securities and non-S% Spot securities, uses location_id 152 as default."

sources:
  - name: csd
    description: "Core system database containing wallet and client data"
    tables:
      - name: wallet_walletoperationlogging
        description: "Wallet transaction logging table containing all wallet operations"
        columns:
          - name: client_id
            description: "Foreign key to client table"
          - name: created
            description: "Transaction creation timestamp"
          - name: currency
            description: "Currency of the transaction"
          - name: total_after
            description: "Total balance after transaction"
          - name: lien_after
            description: "Lien balance after transaction"
          - name: available_after
            description: "Available balance after transaction"
          - name: is_deleted
            description: "Soft delete flag"
          - name: agent_id
            description: "Agent who processed the transaction"
            
      - name: crm_client
        description: "Client master data table"
        columns:
          - name: id
            description: "Primary key for client"
          - name: cid
            description: "Client identifier code"

  - name: ovs
    description: "Operations and valuation system database"
    tables:
      - name: operation_portfoliologging
        description: "Portfolio transaction logging table containing all portfolio operations"
        columns:
          - name: client_id
            description: "Foreign key to client table"
          - name: created
            description: "Transaction creation timestamp"
          - name: security_id
            description: "Foreign key to security table"
          - name: location_id
            description: "Foreign key to location table"
          - name: lien_units_after
            description: "Lien units after transaction"
          - name: available_units_after
            description: "Available units after transaction"
          - name: total_units_after
            description: "Total units after transaction"
            
      - name: crm_client
        description: "Client master data table"
        columns:
          - name: id
            description: "Primary key for client"
          - name: cid
            description: "Client identifier code"
            
      - name: crm_location
        description: "Location master data table"
        columns:
          - name: id
            description: "Primary key for location"
          - name: code
            description: "Location code"
          - name: name
            description: "Location name"
          - name: state
            description: "Location state"

  - name: ovs_mart
    description: "Operations and valuation system data mart"
    tables:
      - name: dim_security
        description: "Security dimension table"
        columns:
          - name: id
            description: "Primary key for security"
          - name: security_name
            description: "Security name"
          - name: security_code
            description: "Security code/symbol"
          - name: security_type
            description: "Security type/category"

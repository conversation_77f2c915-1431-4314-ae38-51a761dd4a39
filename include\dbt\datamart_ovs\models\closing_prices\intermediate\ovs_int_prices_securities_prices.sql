{{ config (materialized = 'ephemeral') }}

with
closing_price as(
	select date, extract(dow from price.date::timestamp) dow
			, concat(extract(year from price.date::timestamp), '_', extract(week from price.date::timestamp)) woy
			, price.adj_security_code security_code
			, sec.security_name
			, sec.security_type security_type
			, price.location_code
			, case when sec.security_type = 'Spot' or sec.security_type = 'FI'	then 'n/a'
					when loc.name is null then price.location_code
				else loc.name
				end "location"
			, price.price closing_price
			, price.price_per_unit closing_price_per_unit
	
	from {{ ref ('ovs_stg_securities_closing_prices') }} price
	left join {{ ref ('ovs_dim_security') }} sec
		on price.adj_security_code = sec.security_code
	left join {{ source ('ovs', 'crm_location') }} loc
		on price.location_code = loc.code
	
	where is_closing_price = 'true' and date >= '2020-01-01'
	),
	
opening_price as(
	select date
			, price.adj_security_code security_code
			, sec.security_name
			, sec.security_type security_type
			, price.location_code
			, case when sec.security_type = 'Spot' or sec.security_type = 'FI'	then 'n/a'
					when loc.name is null then price.location_code
				else loc.name
				end "location"
			, price.price opening_price
			, price.price_per_unit opening_price_per_unit
	
	from {{ ref ('ovs_stg_securities_closing_prices') }} price
	left join {{ ref ('ovs_dim_security') }} sec
		on price.adj_security_code = sec.security_code
	left join {{ source ('ovs', 'crm_location') }} loc
		on price.location_code = loc.code

	where is_opening_price = 'true' and date >= '2020-01-01'
	),

trade_transactions as (
	select *,
			case when security_type = 'Dawa' 
						then security_location
					when security_type = 'OTC'
						then contract_buyer_location_code
					when security_type = 'Spot' or security_type = 'FI'
						then 'n/a'
					else security_location
					end adj_security_location
	
	from {{ source ('exchange_mart', 'fact_trade_individual_transactions') }}
	),

high_low_prices as(
	select date(deal_created_at) date, security_code, security_name, security_type,
			adj_security_location,
			max(matched_price/volume_per_unit) max_price_kg,
			max(matched_price) max_price_per_unit,
			min(matched_price/volume_per_unit) min_price_kg,
			min(matched_price) min_price_per_unit
			 
	from trade_transactions
	where order_type = 'Buy' 
			and (oms_name = 'COMX' or oms_name is null or oms_name = '')
			and (currency = 'Naira' or currency is null or currency = '')
	
	group by date(deal_created_at), security_code, security_name, adj_security_location, security_type
	order by date(deal_created_at), security_code, security_name, adj_security_location, security_type
	),

securities_prices as(
	select closing_price.*,

			opening_price.opening_price,
			case when opening_price.opening_price is null
				then lag(closing_price.closing_price, 1)
							over (partition by closing_price.security_code, closing_price.location order by closing_price.date)
				else opening_price.opening_price
				end as adj_opening_price,

			opening_price.opening_price_per_unit,
			case when opening_price.opening_price_per_unit is null
				then lag(closing_price.closing_price_per_unit, 1)
							over (partition by closing_price.security_code, closing_price.location order by closing_price.date)
				else opening_price.opening_price_per_unit
				end as adj_opening_price_per_unit,

			high_low_prices.max_price_kg, high_low_prices.max_price_per_unit, high_low_prices.min_price_kg, high_low_prices.min_price_per_unit

	from closing_price
	left join opening_price
		on closing_price.date = opening_price.date
		and closing_price.security_code = opening_price.security_code
		and closing_price.location = opening_price.location
	left join high_low_prices
		on closing_price.date = high_low_prices.date
		and closing_price.security_code = high_low_prices.security_code
		and closing_price.location = high_low_prices.adj_security_location
	),

non_null_minmax as( -- When max or min prices are null, becuase no trade on that day, then closing price
	select date, dow, woy, security_code, security_name, security_type, "location", 
			closing_price, closing_price_per_unit,
			adj_opening_price opening_price, adj_opening_price_per_unit opening_price_per_unit,	
	
			case when (max_price_kg is null)  and (closing_price >= adj_opening_price) then closing_price
					when (max_price_kg is null)  and (closing_price < adj_opening_price) then adj_opening_price
				else max_price_kg
				end max_price_kg,
			case when (max_price_per_unit is null)  and (closing_price_per_unit >= adj_opening_price_per_unit) then closing_price_per_unit
					when (max_price_per_unit is null)  and (closing_price_per_unit < adj_opening_price_per_unit) then adj_opening_price_per_unit
				else max_price_per_unit
				end max_price_per_unit,
			case when (min_price_kg is null) and (closing_price > adj_opening_price) then adj_opening_price
					when (min_price_kg is null) and (closing_price <= adj_opening_price) then closing_price
				else min_price_kg
				end min_price_kg,
			case when (min_price_per_unit is null) and (closing_price_per_unit > adj_opening_price_per_unit) then adj_opening_price_per_unit
					when (min_price_per_unit is null) and (closing_price_per_unit <= adj_opening_price_per_unit) then closing_price_per_unit
				else min_price_per_unit
				end min_price_per_unit
	
	from securities_prices
	)

select *

from non_null_minmax


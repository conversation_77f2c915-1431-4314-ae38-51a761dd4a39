{{ config(materialized='ephemeral') }}

WITH activity_streams AS ( -- This is the activity stream for all clients on the exchange system
    -- Step 1: Extract the clock hour as the time band (based on activity time)
    SELECT DISTINCT
        c.cid,
        aas.created activity_time,
        'activity_stream' activity_type
 
    FROM {{ source('comx','crm_client') }} c

    LEFT JOIN {{ source('comx','crm_clientuser') }} cu -- Left join to ensure every user is associated with a cid 

    ON cu.client_id = c.id
 
    LEFT JOIN {{ source('comx', 'administration_activitystream') }} aas

    ON aas.user_id = cu.user_id
 
    WHERE cu.user_id is not Null
    AND cu.id IS NOT NULL    -- Make sure each client gets a client and user id else they are ignored 
    AND c.cid IS NOT NULL    -- Ensure There must be a client ID else user is ignored
    AND c.cid != ''
)
-- Step 2: Calculate the first and last activity time for each user, per day, per hour (time band)

-- Combine both results as all client trade and wallet actions on the system
, client_actions AS (
	SELECT 
		action_date activity_time,
		cid,
		action_type activity_type
	FROM {{ ref('stg_client_wallet_trade_stream') }}
		
	UNION ALL
	
	SELECT 
			activity_time,
			cid,
			activity_type
	FROM 
		activity_streams
	
)
, activity_with_time_band AS (
	SELECT 
		cid,
		activity_time,
		activity_type,
        DATE(activity_time) AS activity_date,
        EXTRACT(HOUR FROM activity_time) AS activity_hour
	FROM client_actions 
)
SELECT *
FROM activity_with_time_band


from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.bash  import BashOperator
from custom_functions.airflow_email_util import failure_email
import os
from dotenv import load_dotenv, find_dotenv
load_dotenv(find_dotenv())

# Validate critical environment variables
required_env_vars = [
    'POSTGRES_ADDRESS', 'POSTGRES_DBNAME', 'POSTGRES_USERNAME',
    'POSTGRES_PASSWORD', 'POSTGRES_PORT', 'SUPABASE_PASSWORD',
    'SUPABASE_CONN_STRING', 'VECTOR_DB_COLLECTION', 'OPENAI_API_KEY'
]

missing_vars = [var for var in required_env_vars if not os.getenv(var)]
if missing_vars:
    raise ValueError(f"Missing required environment variables: {missing_vars}")


PROJECT_ROOT="/usr/local/airflow"
PATH_TO_VENV="/usr/local/airflow/embedvenv/bin/activate"
PATH_TO_SCRIPT="/usr/local/airflow/tests/scripts/eel/"


default_args  = {
     'owner' : '<PERSON>',
     'retries' : 0,
     'retry_dalay' : timedelta(minutes=2),
     'on_failure_callback': failure_email,
}
with DAG(
    dag_id = 'embed_docs_dag',
    default_args = default_args,
    description =  'Embed metadata description into chroma',
    start_date =  datetime(2025, 5, 1),
     schedule=None,
    catchup=False,
    ) as dag:
    
    task1 =  BashOperator(
        task_id = 'extract_embed_load',
        bash_command = 'source $PATH_TO_VENV && python3 eel.py',
        cwd=PATH_TO_SCRIPT,
        env={"PATH_TO_VENV": PATH_TO_VENV,"PROJECT_ROOT":PROJECT_ROOT,
            'POSTGRES_ADDRESS': os.getenv('POSTGRES_ADDRESS'),
            'POSTGRES_DBNAME': os.getenv("POSTGRES_DBNAME"),
            'POSTGRES_USERNAME': os.getenv("POSTGRES_USERNAME"),
            'POSTGRES_PASSWORD': os.getenv("POSTGRES_PASSWORD"),
            'POSTGRES_PORT': os.getenv("POSTGRES_PORT"),
            'SUPABASE_PASSWORD': os.getenv("SUPABASE_PASSWORD"),
            'SUPABASE_CONN_STRING': os.getenv("SUPABASE_CONN_STRING"),
            'VECTOR_DB_COLLECTION': os.getenv("VECTOR_DB_COLLECTION"),
            'OPENAI_API_KEY': os.getenv("OPENAI_API_KEY"),
            'GOOGLE_API_KEY': os.getenv("GOOGLE_API_KEY")},
            # "/home/<USER>/development/airflow_2024"
    )


task1

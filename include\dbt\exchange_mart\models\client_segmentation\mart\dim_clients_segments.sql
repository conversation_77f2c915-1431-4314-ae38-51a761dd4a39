{{ config(materialized='table') }}

WITH customer_segments AS (
    SELECT 
        el.cid,
        el.last_seen,
        el.time_since_last_seen,
        el.last_action_time,
        el.last_action_done,
        el.time_since_last_action,
        el.active_status,

        COALESCE(bnt.best_notification_hour, '8:00') best_notification_hour,
        bnt. activity_hour_frequency,
        bnt.avg_online_duration_within_hour,

        trc.boards,
        trc.commodities

    FROM 
        {{ ref('int_engagement_level') }} el

    LEFT JOIN
        {{ ref('int_client_bnt') }} bnt
        ON bnt.cid = el.cid
        
    LEFT JOIN
         {{ ref('int_client_traded_commodities') }} trc
        ON el.cid = trc.trans_cid
    
)

SELECT 

    cid,
    last_seen,
    time_since_last_seen,
    last_action_time,
    last_action_done,
    time_since_last_action,
    active_status,
    best_notification_hour,
    activity_hour_frequency,
    avg_online_duration_within_hour,

    boards,
    commodities
FROM 
    customer_segments

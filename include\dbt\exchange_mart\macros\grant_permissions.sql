{% macro grant_permissions(model_ref) %}
    {% set db = model_ref.database %}
    {% set schema = model_ref.schema %}
    {% set table = model_ref.identifier %}

    {% if db == 'dwhtest' %}
        {% if schema == 'exchange_mart' %}
            -- Grant for exchange_reader (only dim_clients_segments)
            {% if table == 'dim_clients_segments' %}
                GRANT USAGE ON SCHEMA {{ schema }} TO exchange_reader;
                GRANT SELECT ON TABLE {{ schema }}.{{ table }} TO exchange_reader;
            {% endif %}

            -- Grant for dwhtestreader
            GRANT USAGE ON SCHEMA {{ schema }} TO dwhtestreader;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO dwhtestreader;

            -- Grant for sherlock_knowledge_user
            GRANT USAGE ON SCHEMA {{ schema }} TO sherlock_knowledge_user;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO sherlock_knowledge_user;


        {% elif schema == 'trade_mart' %}
            -- Grant for dwhtestreader
            GRANT USAGE ON SCHEMA {{ schema }} TO dwhtestreader;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO dwhtestreader;

            -- Grant for sherlock_knowledge_user
            GRANT USAGE ON SCHEMA {{ schema }} TO sherlock_knowledge_user;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO sherlock_knowledge_user;

        {% elif schema == 'banking_mart' %}
            -- Grant for dwhtestreader
            GRANT USAGE ON SCHEMA {{ schema }} TO dwhtestreader;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO dwhtestreader;

            -- Grant for sherlock_knowledge_user
            GRANT USAGE ON SCHEMA {{ schema }} TO sherlock_knowledge_user;
            GRANT SELECT ON ALL TABLES IN SCHEMA {{ schema }} TO sherlock_knowledge_user;
        {% endif %}

    {% endif %}
{% endmacro %}

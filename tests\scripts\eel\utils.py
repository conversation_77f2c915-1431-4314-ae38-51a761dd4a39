import json
import logging
import sys
from typing import Optional, List
from pathlib import Path
import os
import yaml
from sqlalchemy import text
import sqlalchemy
from connect import create_engine_to_source_datawarehouse
import psycopg2


source_engine = create_engine_to_source_datawarehouse()

def get_logger():
    # Configure logging
    logger = logging.getLogger("eel")
    logger.setLevel(logging.INFO)

    # Create a stdout handler
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)

    # Add the handler to the logger
    logger.addHandler(handler)

    return logger

logger = get_logger()


test_schema = "test_schema.yml"
def read_yml(yml_path):
    # Open and read the YAML file
    with open(yml_path, "r") as file:
        data = yaml.safe_load(file)
    return data

def load_considerations(file_path):
    with open(file_path, 'r') as fp:
        data = json.load(fp)
    return data

consideration = load_considerations('./consideration.json') # This file contains optional/additional information on the table description. 
                                                            # This file will provide a way for the developer to modify description to optimise the model.
                                                            # This is because the desription in the schema.yml files will only be modifiable per business needs and not development need

def format_col(tablecol):
    col_desc = ""
    for i,v in tablecol.items():
        if i == 'tests':
            continue
        if i == 'name' and v in ('first_name', 'last_name', 'middle_name', 'full_name'):
            continue
            
        col_desc += f"\t{i}: {v}\n"

    return col_desc

def schema_to_system(schema_name):
    if schema_name in ['exchange_mart', 'comx_mart','ovs_mart']:
        return 'Africa Exchange'
    elif schema_name in ['workbench_mart', 'trade_workbench_mart', 'trade_mart']:
        return "Workbench"
    elif schema_name in ['banking_mart']:
        return "Cudie"
def parse_model(model, schema_name):
    try:
        system_name = schema_to_system(schema_name)
        tablename = model['name']
        tablecols = model['columns']
        table_opt_context = f"Table Name: `{schema_name}.{tablename}`\nThis table is attributed to {system_name} System. \nTable description: "
        table_opt_context += model['description']
        table_info = table_opt_context
        table_row_context = "\nHere are some relevant example rows (values in the same order as columns above)\n"
        row_context = get_row_context(source_engine, table_name=tablename, schema_name=schema_name) or ''
        table_row_context += row_context
        formated_columns = "\n".join([format_col(tablecol) for tablecol in tablecols])
        table_info += f"\nIt contains the following columns:\n{formated_columns}"
        # Optional: Include consideration
        print(schema_name)
        schema_cons = list(filter(lambda x: x['schema'] == schema_name, consideration))[0]
        table_consideration = schema_cons.get(tablename)
        if table_consideration is not None:
            table_info += f"{table_consideration}"
        table_info += table_row_context
        return table_info
    except psycopg2.errors.UndefinedTable:
        logger.error(f"Table: {tablename} not found. Skipping")
        return None


# Find the "include" directory
project_root = Path(os.getenv("PROJECT_ROOT")) or Path(os.path.dirname(os.path.realpath(__file__)))
logger.info(f"Root Path: {project_root}")
dbt_projects_path = project_root / "include" / "dbt"

def get_prject_schema_file_paths(proj_name):
    dbt_project_dir = dbt_projects_path / proj_name
    # project_root = Path(project_root)  # Ensure it's a Path object
    constraints = {'run', 'target', 'compiled'}
    # project_root = Path(project_root)  # Ensure it's a Path object
    mart_schema_files = {file_path for file_path in dbt_project_dir.rglob("mart/schema.yml") if set(file_path.parts).isdisjoint(constraints)}


    return set(mart_schema_files)

def parse_project(proj_name):  
    models = []
    schema_files_paths = get_prject_schema_file_paths(proj_name)
    for schema_file in schema_files_paths:
        schemas = read_yml(schema_file)
        models += schemas['models']

    tags= [proj_name]
    parsed_models = [parse_model(model, schema_name=proj_name) for model in models]
    # for model in models:

    return parsed_models, tags

def get_all_projects(include_projects=None, exclude_projects=None):
    
    all_projects = set(os.listdir(dbt_projects_path))
    if include_projects and exclude_projects:
        raise ValueError("Can only specify one of these: exclude_projects or include_projects")
    elif include_projects:
         relevenat_projects = all_projects.intersection(include_projects)
    elif exclude_projects:
        relevenat_projects = all_projects.difference(exclude_projects)
    else:
        relevenat_projects = all_projects
    return relevenat_projects



def get_row_context(source_engine, table_name, schema_name):
        logger.info(f"Indexing rows in table: {schema_name}.{table_name}")
        table_row_context = ""
        limit = 3
        try:
            query = text(f"SELECT * FROM {schema_name}.{table_name} LIMIT {limit}")
            with source_engine.connect() as conn:
                cursor = conn.execute(query)
                result = cursor.fetchall()
                row_tups = []
                for row in result:
                    row_tups.append(tuple(row))

            # index each row, put into vector store index
            for t in row_tups:
                table_row_context += str(t)  + "\n"

            return table_row_context
        except sqlalchemy.exc.ProgrammingError as e:
            # Extract the actual PostgreSQL error code and message
            orig_error = e.orig  # This comes from psycopg2

            if isinstance(orig_error, psycopg2.errors.UndefinedTable):
                logger.error(f"Table for model {table_name} not found")
                raise psycopg2.errors.UndefinedTable
            elif isinstance(orig_error, psycopg2.errors.UndefinedColumn):
                logger.error(f"A referenced column not found. Details: {orig_error.pgerror}")
            else:
                logger.error(f"SQL Programming Error: {orig_error.pgerror}")  # Generic error
                

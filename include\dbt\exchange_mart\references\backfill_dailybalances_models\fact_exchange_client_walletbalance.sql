
-- **HIS<PERSON><PERSON>CALS**
-- -- Create fact_exchange_client_walletbalance Table
-- DELETE FROM exchange_mart.fact_exchange_client_walletbalance;

-- DROP TABLE exchange_mart.fact_exchange_client_walletbalance

-- CREATE TABLE exchange_mart.fact_exchange_client_walletbalance (
-- 				wallet_date DATE,
-- 				cid VARCHAR(25),  
-- 				currency TEXT,
-- 				total_wallet_balance DOUBLE PRECISION,
-- 				lien_wallet_balance DOUBLE PRECISION,
-- 				available_wallet_balance DOUBLE PRECISION, 
-- 				latest_trans_at TIMESTAMP,
-- 				PRIMARY KEY (wallet_date, cid, currency)
-- 	);


-- -- -- Insert Historic Data Into The Table
-- INSERT INTO exchange_mart.fact_exchange_client_walletbalance (wallet_date,
-- 															cid,
-- 															currency,
-- 															total_wallet_balance,
-- 															lien_wallet_balance,
-- 															available_wallet_balance,
-- 															latest_trans_at)


-- WITH 
-- transactions AS(
-- 	-- Extract transactions with details and assign a row index to each transaction per client and day
-- 	SELECT trans.created, trans.updated, trans.transaction_type, trans.currency, trans.amount
-- 			, trans.total_after, trans.lien_after, trans.available_after
-- 			, trans.ref_id, client.cid
-- 			, ROW_NUMBER() OVER (PARTITION BY client.cid, trans.currency, date(trans.created) ORDER BY trans.created DESC) row_index
-- 				-- To get the last transaction for each client for each day. Notice the date function in the partition clause, it is to ensure the partition is by the entire day and not a timestamp
			
	
-- 	FROM csd.wallet_walletoperationlogging trans
-- 	LEFT JOIN csd.crm_client client 
-- 	ON trans.client_id = client.id

-- 	WHERE trans.is_deleted IS false and trans.agent_id is not null
-- 	),
	
-- balance_per_trans AS(
-- 	-- Retrieve only the last transaction for each client per day
-- 	SELECT *
-- 			, date(created) created_date
	
-- 	FROM transactions
	
-- 	WHERE row_index = 1
-- 	),

-- date_dim AS (
--         -- Generate a date range for the required period (adjustable based on business needs)
-- 	SELECT date_actual
	
-- 	FROM afex_gen_mart.dim_date
    
--     WHERE date_actual between '2021-05-18' AND (current_date - 1) 	--2021-05-18 is the first date on the wallet_walletoperationlogging table
-- 	),

-- client_start_date AS (
--         -- Determine the first transaction date for each client
--     SELECT cid
-- 			, currency
--         	, DATE(MIN(created)) AS start_date
	
--     FROM transactions
--     GROUP BY cid, currency
-- 	),

-- clients_and_dates AS (
--         -- Generate a date series for each client, starting from their first transaction date
--     SELECT client_start_date.*
--             , date_dim.date_actual
	
--     FROM client_start_date
--     LEFT JOIN date_dim
--     ON client_start_date.start_date <= date_dim.date_actual
-- 	),

-- balance_per_day AS(
--         -- Now we join this date series for each client to the balances and total amount table, to get the balance for the days they did transact, however we are not done yet.
--         -- We need to get the latest transaction value for days with no transactions after the client started transacting, and we require and helper column for that, for which we created running total field
--         -- This field would be concatenated with the client_id to create a unique identifier which would be used in identifying the latest transaction at each level. This is done in the following cte
-- 	SELECT cad.date_actual wallet_date
-- 			, cad.cid actual_cid -- This cid is the actual (think final) because that from the balance table would have null values for days without transaction for each client
-- 			, cad.currency actual_currency	
-- 			, bal.*
-- 			, SUM(CASE WHEN bal.amount IS NULL THEN 0 ELSE 1 END)
--                     OVER (PARTITION BY cad.cid, cad.currency
-- 							 ORDER BY cad.date_actual) running_total_transactions
	
-- 	FROM clients_and_dates cad
-- 	LEFT JOIN balance_per_trans bal
-- 		ON cad.date_actual = bal.created_date
-- 		AND cad.cid = bal.cid
-- 		AND cad.currency = bal.currency
-- 	),

-- unique_identifier AS (
--         -- Add a unique identifier to associate transactions with no-transaction days to the last known balance
-- 	SELECT *
-- 			, concat(actual_cid, '_', actual_currency, '_', running_total_transactions) unique_balance_identifier
--                 -- Here is the unique identifier to identify each transaction day to enable the reversion to the last known balance, for days where there are no transactions

-- 	FROM balance_per_day
-- 		),

-- final AS (
--         -- Compute the account balance for all days, using the last known balance for days without transactions
-- 	SELECT wallet_date
--             , actual_cid AS cid
-- 			, actual_currency AS currency
-- 			, first_value(total_after) OVER (PARTITION BY unique_balance_identifier
-- 												 ORDER BY created_date) total_wallet_balance
-- 			, first_value(lien_after) OVER (PARTITION BY unique_balance_identifier
-- 												 ORDER BY created_date) lien_wallet_balance
-- 			, first_value(available_after) OVER (PARTITION BY unique_balance_identifier
-- 												 ORDER BY created_date) available_wallet_balance
-- 			, first_value(created) OVER (PARTITION BY unique_balance_identifier
-- 												 ORDER BY created_date) latest_trans_at

-- 	FROM unique_identifier
-- 	)

-- -- Final selection of records to insert into the fact table	
-- SELECT wallet_date,
-- 		cid,
-- 		currency,
-- 		total_wallet_balance,
-- 		lien_wallet_balance,
-- 		available_wallet_balance,
-- 		latest_trans_at
	
-- FROM final

packages:
  - package: dbt-labs/codegen
    version: 0.12.1
  - package: dbt-labs/dbt_utils
    version: 1.2.0
  - package: dbt-labs/audit_helper
    version: 0.12.0
  - package: calogica/dbt_expectations
    version: 0.10.3


# #### Based on the system the project is being run on, uncomment one and ony one of the sections below.



# # To Run via docker container on server
#   - local: "/usr/local/airflow/include/dbt/datamart_workbench"
#   - local: "/usr/local/airflow/include/dbt/datamart_trade"
#   - local: "/usr/local/airflow/include/dbt/datamart_comx" 
#   - local: "/usr/local/airflow/include/dbt/datamart_ovs"




# #  When debugging on local system - Oluwatomisin
  - local: "/Users/<USER>/OneDrive - AFEX Commodities Exchange Limited/Documents/Data Models & Pipeline/data_pipelines/dbt_pipeline/Include/airflow_2024/include/dbt/datamart_workbench"
  - local: "/Users/<USER>/OneDrive - AFEX Commodities Exchange Limited/Documents/Data Models & Pipeline/data_pipelines/dbt_pipeline/Include/airflow_2024/include/dbt/datamart_trade"
  - local: "/Users/<USER>/OneDrive - AFEX Commodities Exchange Limited/Documents/Data Models & Pipeline/data_pipelines/dbt_pipeline/Include/airflow_2024/include/dbt/datamart_comx" 
  - local: "/Users/<USER>/OneDrive - AFEX Commodities Exchange Limited/Documents/Data Models & Pipeline/data_pipelines/dbt_pipeline/Include/airflow_2024/include/dbt/dbt/datamart_ovs"



# #  When debugging on local system - Muhammad Yakub
  # - local: "/home/<USER>/development/airflow_2024/include/dbt/datamart_workbench"
  # - local: "/home/<USER>/development/airflow_2024/include/dbt/datamart_trade"
  # - local: "/home/<USER>/development/airflow_2024/include/dbt/datamart_comx" 
  # - local: "/home/<USER>/development/airflow_2024/include/dbt/datamart_ovs"




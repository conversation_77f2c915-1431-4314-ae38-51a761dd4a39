WITH

individual_clients AS(
    SELECT *
    FROM {{ source('comx_mart', 'dim_client') }}
    
    WHERE user_account_type = 'Individual'
),

all_information AS(
	SELECT cli.*, ind.bvn, ind.dob, ind.lga,
			ind.gender,ind.user_id,ind.nok_name,ind.nok_phone,
			ind.nationality,ind.nok_address,
			ind.id_card_type,ind.id_card_image,ind.id_card_number,
			ind.marital_status,ind.prompt_message,ind.approval_status,
			ind.state_of_origin,ind.nok_relationship,ind.resident_address,
			ind.id_card_issue_date,ind.state_of_residence,
			ind.id_card_expiry_date,ind.politically_exposed,
			ind.political_experience,ind.brokerage_community_id,
			row_number() over(partition by cli.cid) row_number_index

	FROM individual_clients cli

	LEFT JOIN {{ source('comx', 'crm_individualclient') }} ind
	ON cli.rnb = ind.rnb

	ORDER BY cli.created DESC
),

required_columns AS(
	SELECT 
		cid, created, account_type, country_code,
		is_synced_wb, referral_code, is_afex_broker,
		is_kyc_complete, user_account_type, used_referral_code,
		country, region, subregion, bvn, dob, lga, gender,
		nationality, id_card_type,id_card_number, marital_status,
		state_of_origin, resident_address, state_of_residence,
		politically_exposed, political_experience,brokerage_community_id
	FROM all_information
	WHERE row_number_index = 1
),

new_columns AS(
	SELECT *,
		CASE WHEN LEFT(used_referral_code,3) ILIKE '%COMX%'
			THEN 'referred'
		ELSE 'not referred'
		END AS is_referred,

		CASE WHEN LENGTH(bvn) = 11
			THEN 'has bvn'
		ELSE 'no bvn'
		END AS has_bvn,

		CASE WHEN dob::text ~ '^\d{1,4}-\d{2}-\d{2}$'
			THEN 'has dob'
		ELSE 'no dob'
		END AS has_dob,

		CASE WHEN lga IN (select "name" from {{ source('comx', 'account_lga')}} )
			THEN 'has lga'
		ELSE 'no lga'
		END AS has_lga,

		CASE WHEN (gender ILIKE '%female%' OR gender ILIKE '%male%')
			THEN 'has gender'
		ELSE 'no gender'
		END AS has_gender,

		CASE WHEN state_of_origin IN (select "name" from {{ source('comx', 'account_state')}} )
			THEN 'has state_of_origin'
		ELSE 'no state_of_origin'
		END AS has_stateoforigin,

		CASE WHEN state_of_residence IN (select "name" from {{ source('comx', 'account_state')}} )
			THEN 'has state_of_residence'
		ELSE 'no state_of_residence'
		END AS has_stateofresidence,

		EXTRACT(
			YEAR FROM AGE(
				CURRENT_DATE, dob)) age,
		EXTRACT(
			YEAR FROM created) created_year,
		EXTRACT(
			MONTH FROM created) created_month,
		EXTRACT(
			DAY FROM created) created_dayofmonth,
		EXTRACT(
			DOW FROM created) created_dayofweek

	FROM required_columns
),

all_client_info AS(
	SELECT
		cid,
		created,
	
		CASE WHEN account_type IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY account_type) FROM new_columns)
		ELSE account_type
		END AS account_type,
	
		CASE WHEN country_code IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY country_code) FROM new_columns)
		ELSE country_code
		END AS country_code,
	
		is_synced_wb,
		is_afex_broker,
		is_kyc_complete,
	
		CASE WHEN user_account_type IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY user_account_type) FROM new_columns)
		ELSE user_account_type
		END AS user_account_type,
		
		CASE WHEN country IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY country) FROM new_columns)
		ELSE country
		END AS country,
	
		CASE WHEN region IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY region) FROM new_columns)
		ELSE region
		END AS region,
	
		CASE WHEN subregion IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY subregion) FROM new_columns)
		ELSE subregion
		END AS subregion,
	
		CASE WHEN lga IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY lga) FROM new_columns)
		ELSE lga
		END AS lga,
		
		CASE WHEN gender IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY gender) FROM new_columns)
		ELSE gender
		END AS gender,
		
		CASE WHEN nationality IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY nationality) FROM new_columns)
		ELSE nationality
		END AS nationality,
		
		COALESCE(marital_status, 'Single') marital_status,
	
		
		CASE WHEN state_of_origin IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY state_of_origin) FROM new_columns)
		ELSE state_of_origin
		END AS state_of_origin, state_of_origin state_of_origin_2,
	
		CASE WHEN state_of_residence IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY state_of_residence) FROM new_columns)
		ELSE state_of_residence
		END AS state_of_residence,
	
		CASE WHEN politically_exposed IS NULL
			THEN (SELECT MODE() WITHIN GROUP (ORDER BY politically_exposed) FROM new_columns)
		ELSE politically_exposed
		END AS politically_exposed,
	
		is_referred,
		has_bvn,
		has_dob,
		has_lga,
		has_gender,
		has_stateoforigin,
		has_stateofresidence,
	
		CASE WHEN age IS NULL
			THEN (SELECT percentile_cont(0.5) WITHIN GROUP (ORDER BY age) FROM new_columns)
		ELSE age
		END AS age,
	
		created_year,
		created_month,
		created_dayofmonth,
		created_dayofweek
	
	FROM new_columns
)

select * from all_client_info
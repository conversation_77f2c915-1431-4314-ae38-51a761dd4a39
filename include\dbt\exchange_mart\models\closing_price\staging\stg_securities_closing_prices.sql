{{ config (materialized = 'ephemeral') }}

with 
base as(
	select *
		, case when security_code like 'CCO' then 'COC'
				when security_code like '%CCO%' then replace(security_code, 'CCO', 'COC')
			else security_code
			end as updated_security_code
	from {{ source('ovs', 'databank_historicalprice') }}
	where security_code not in ('CSCN')
	),
	
historical_sec_prices as(
	select price.*
			, case when length (price.updated_security_code) = 3
					then concat('S', price.updated_security_code)
				else price.updated_security_code
				end as adj_security_code
			, case when price.is_closing_price is true then 'Closing Price'
					when price.is_opening_price is true then 'Opening Price'
				else null
				end as price_type
			, dim_date.day_of_month, dim_date.day_of_quarter, dim_date.day_of_year
	
	from base price
	left join {{ source ('afex_gen_mart', 'dim_date') }} dim_date
	on date(price.date) = date(dim_date.date_actual)
	),

deduplicated as(
	select row_number() over (partition by "date", adj_security_code, price_type, location_code order by price, security_code) row_num
		, "date"
		, price
		, case when left(adj_security_code, 1) = 'S' and right(adj_security_code, 3) != 'COC' and date < '2021-10-02' and price >= 1000
					then price / 100
				when left(adj_security_code, 1) = 'S' and right(adj_security_code, 3) = 'COC' and date < '2021-10-02' and price >= 10000
					then price / 100
				when adj_security_code = 'NPRL' and date < '2021-10-02' and price>= 10000 
					then price / 100
				when adj_security_code = 'NPRL' and date < '2021-10-02' and price>= 100000 
					then price / 1000
			else price
			end as price_per_kg
		, price_type
		, location_code
		, security_code
		, adj_security_code
		, price_per_unit
		, is_closing_price
		, is_opening_price
		, day_of_month
		, day_of_quarter
		, day_of_year
	
	from historical_sec_prices
	where price_type is not null
	)

select *
from deduplicated
where row_num = 1